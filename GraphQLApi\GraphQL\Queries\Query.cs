﻿using GraphQLApi.Services;
using Shared.GraphQL.Models;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;

        public Query(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
        }

        public async Task<IEnumerable<Worker>> GetAllWorkers()
        {
            return await _workerService.GetAllWorkersAsync();
        }

        public async Task<Worker?> GetWorkerById(int id)
        {
            return await _workerService.GetWorkerByIdAsync(id);
        }

        public async Task<IEnumerable<Training>> GetAllTrainings()
        {
            return await _trainingService.GetAllTrainingsAsync();
        }

        public async Task<Training?> GetTrainingById(int id)
        {
            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<IEnumerable<Trade>> GetAllTrades()
        {
            return await _tradeService.GetAllTradesAsync();
        }

        public async Task<Trade?> GetTradeById(int id)
        {
            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<IEnumerable<Skill>> GetAllSkills()
        {
            return await _skillService.GetAllSkillsAsync();
        }

        public async Task<Skill?> GetSkillById(int id)
        {
            return await _skillService.GetSkillByIdAsync(id);
        }
    }
}
