﻿using GraphQLApi.Services;
using Shared.GraphQL.Models;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;

        public Query(IWorkerService workerService, ITrainingService trainingService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
        }

        public async Task<IEnumerable<Worker>> GetAllWorkers()
        {
            return await _workerService.GetAllWorkersAsync();
        }

        public async Task<Worker?> GetWorkerById(int id)
        {
            return await _workerService.GetWorkerByIdAsync(id);
        }

        public async Task<IEnumerable<Training>> GetAllTrainings()
        {
            return await _trainingService.GetAllTrainingsAsync();
        }

        public async Task<Training?> GetTrainingById(int id)
        {
            return await _trainingService.GetTrainingByIdAsync(id);
        }
    }
}
