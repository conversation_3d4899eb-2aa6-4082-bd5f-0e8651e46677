﻿using GraphQLApi.Services;
using Shared.GraphQL.Models;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITraingService _trainingService;

        public Query(IWorkerService workerService, ITraingService traingService)
        {
            _workerService = workerService;
            _trainingService = traingService;
        }

        public async Task<IEnumerable<Worker>> GetAllWorkers()
        {
            return await _workerService.GetAllWorkersAsync();
        }

        public async Task<Worker?> GetWorkerById(int id)
        {
            return await _workerService.GetWorkerByIdAsync(id);
        }
        public async Task<IEnumerable<Training>> GetAllTrainingsAsync()
        {
            return await _trainingService.GetAllTrainingsAsync();
        }
        public async Task<Training?> GetTrainingByIdAsync(int id)
        {
            return await _trainingService.GetTrainingByIdAsync(id);
        }
    }
}
