using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services;
public class TrainingService(
    IDbContextFactory<AppDbContext> contextFactory,
    ILogger<TrainingService> logger
    ) : ITraingService
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory = contextFactory;
    private readonly ILogger<TrainingService> _logger = logger;

    public async Task<IEnumerable<Training>> GetAllTrainingsAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings.ToListAsync();
    }
    public async Task<Training?> GetTrainingByIdAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings.FindAsync(id);
    }
    public async Task<Training> CreateTrainingAsync(Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        // ! check if trainings exists
        var existingTraining = await context.Trainings.FirstOrDefaultAsync(t => t.Name == training.Name);
        if (existingTraining != null)
        {
            throw new GraphQLException(new Error("Validation", $"A training with name '{training.Name}' already exists."));
        }
        training.Name = training.Name.ToLower();
        context.Trainings.Add(training);
        await context.SaveChangesAsync();
        return training;
    }
    public async Task<Training?> UpateTrainingAsync(int id, Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingTraining = await context.Trainings.FindAsync(id);
        if (existingTraining == null)
        {
            return null;
        }
        existingTraining.Name = training.Name;
        training.Workers.ForEach(w => existingTraining.Workers.Add(w));
        await context.SaveChangesAsync();
        return existingTraining;
    }
    public async Task<bool> DeleteTrainingAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingTraining = await context.Trainings.FindAsync(id);
        if (existingTraining == null)
        {
            return false;
        }
        try
        {
            context.Trainings.Remove(existingTraining);
            await context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in deleting training with ID {id}", id);
            return false;
        }

    }
}