using HotChocolate.Types;
using HotChocolate;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class WorkerType : ObjectType<Worker>
    {
        protected override void Configure(IObjectTypeDescriptor<Worker> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Name).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Company).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Trade).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Skill).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.NationalId).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Gender).Type<NonNullType<StringType>>();
            
            // Optional fields
            descriptor.Field(t => t.PhotoUrl).Type<StringType>();
            descriptor.Field(t => t.PhoneNumber).Type<StringType>();
            descriptor.Field(t => t.Email).Type<StringType>();
            descriptor.Field(t => t.InductionDate).Type<DateTimeType>();
            descriptor.Field(t => t.MedicalCheckDate).Type<DateTimeType>();
            
            // Numeric fields
            descriptor.Field(t => t.Age).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.TrainingsCompleted).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.ManHours).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Rating).Type<NonNullType<FloatType>>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<StringType>();
        }
    }
}
