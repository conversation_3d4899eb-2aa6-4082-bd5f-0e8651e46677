using System.IO;
using Microsoft.Extensions.Configuration;

namespace GraphQLApi.Services
{
    public class PhotoService : IPhotoService
    {
        private readonly IHikvisionService _hikvisionService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<PhotoService> _logger;
        private readonly string _localStoragePath;

        public PhotoService(
            IHikvisionService hikvisionService,
            IConfiguration configuration,
            ILogger<PhotoService> logger)
        {
            _hikvisionService = hikvisionService;
            _configuration = configuration;
            _logger = logger;
            _localStoragePath = _configuration["PhotoStorage:LocalPath"] ?? "wwwroot/photos";
            
            // Ensure storage directory exists
            if (!Directory.Exists(_localStoragePath))
            {
                Directory.CreateDirectory(_localStoragePath);
            }
        }

        public async Task<string> UploadPhotoAsync(Stream photoStream, string fileName, PhotoStorageType storageType)
        {
            try
            {
                switch (storageType)
                {
                    case PhotoStorageType.Local:
                        return await SaveLocalPhotoAsync(photoStream, fileName);
                    case PhotoStorageType.Hikvision:
                        return await _hikvisionService.UploadWorkerPhotoAsync(fileName, photoStream);
                    default:
                        throw new ArgumentException("Invalid storage type", nameof(storageType));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo {FileName}", fileName);
                throw;
            }
        }

        public async Task<Stream> GetPhotoAsync(string photoUrl)
        {
            try
            {
                if (IsLocalPhoto(photoUrl))
                {
                    var path = GetLocalPhotoPath(photoUrl);
                    return new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read, 4096, FileOptions.Asynchronous | FileOptions.SequentialScan);
                }
                else
                {
                    var workerId = ExtractWorkerIdFromHikvisionUrl(photoUrl);
                    return await _hikvisionService.GetWorkerPhotoAsync(workerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photo {PhotoUrl}", photoUrl);
                throw;
            }
        }

        public async Task DeletePhotoAsync(string photoUrl)
        {
            try
            {
                if (IsLocalPhoto(photoUrl))
                {
                    var path = GetLocalPhotoPath(photoUrl);
                    if (File.Exists(path))
                    {
                        File.Delete(path);
                    }
                }
                else
                {
                    var workerId = ExtractWorkerIdFromHikvisionUrl(photoUrl);
                    await _hikvisionService.DeleteWorkerPhotoAsync(workerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo {PhotoUrl}", photoUrl);
                throw;
            }
        }

        public async Task<string> TransferPhotoToHikvisionAsync(string sourcePhotoUrl, string workerId)
        {
            try
            {
                using var photoStream = await GetPhotoAsync(sourcePhotoUrl);
                var hikvisionUrl = await _hikvisionService.UploadWorkerPhotoAsync(workerId, photoStream);
                
                // Delete the local photo if it exists
                if (IsLocalPhoto(sourcePhotoUrl))
                {
                    await DeletePhotoAsync(sourcePhotoUrl);
                }

                return hikvisionUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring photo to Hikvision for worker {WorkerId}", workerId);
                throw;
            }
        }

        private async Task<string> SaveLocalPhotoAsync(Stream photoStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            var filePath = System.IO.Path.Combine(_localStoragePath, uniqueFileName);
            
            using var fileStream = File.Create(filePath);
            await photoStream.CopyToAsync(fileStream);
            
            return $"/photos/{uniqueFileName}";
        }

        private bool IsLocalPhoto(string photoUrl)
        {
            return photoUrl.StartsWith("/photos/");
        }

        private string GetLocalPhotoPath(string photoUrl)
        {
            var fileName = System.IO.Path.GetFileName(photoUrl);
            return System.IO.Path.Combine(_localStoragePath, fileName);
        }

        private string ExtractWorkerIdFromHikvisionUrl(string photoUrl)
        {
            //@TODO: Implement logic to extract worker ID from Hikvision URL
            // This will depend Hikvision URL format
            return photoUrl.Split('/').Last();
        }
    }
} 