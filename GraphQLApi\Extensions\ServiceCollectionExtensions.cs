using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using GraphQLApi.Services;
using GraphQLApi.GraphQL.Queries;
using GraphQLApi.GraphQL.Mutations;
using Shared.GraphQL.Models;
using Shared.GraphQL.Types;

namespace GraphQLApi.Extensions
{
    public static class ServiceCollectionExtensions
    {

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddScoped<IWorkerService, WorkerService>();
            services.AddScoped<ITraingService, TrainingService>();
            services.AddScoped<IPhotoService, PhotoService>();
            services.AddScoped<IWorkerAttendanceService, WorkerAttendanceService>();
            return services;
        }

        public static IServiceCollection AddHikvisionServices(this IServiceCollection services, IConfiguration configuration)
        {
            var baseUrl = configuration["HikvisionApi:BaseUrl"] 
                ?? throw new ArgumentNullException("HikvisionApi:BaseUrl is missing in configuration");
            
            services.AddHttpClient<IHikvisionService, HikvisionService>(client =>
            {
                client.BaseAddress = new Uri(baseUrl);
            });
            services.AddScoped<IHikvisionService, HikvisionService>();

            return services;
        }

        public static IServiceCollection AddGraphQLServices(this IServiceCollection services)
        {
            services
                .AddGraphQLServer()
                .ModifyRequestOptions(opt => opt.IncludeExceptionDetails = true)//Make sure to turn off in production
                .AddQueryType<Query>()
                .AddMutationType<Mutation>()
                .AddType<WorkerType>()
                .AddType<TaskType>()
                .AddType<WorkerAttendanceType>()
                .AddType<ToolboxSessionType>();

            return services;
        }
    }
} 