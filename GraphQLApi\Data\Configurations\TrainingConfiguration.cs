using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingConfiguration : IEntityTypeConfiguration<Training>
    {
        public void Configure(EntityTypeBuilder<Training> builder)
        {
            builder.ToTable("Trainings");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Description)
                .HasMaxLength(500);

            builder.Property(e => e.StartDate)
                .HasColumnType("datetime2");

            builder.Property(e => e.Duration)
                .HasColumnType("time");

            builder.Property(e => e.TrainingType)
                .HasMaxLength(50);

            builder.Property(e => e.Trainer)
                .HasMaxLength(100);

            // Unique constraint on Name
            builder.HasIndex(e => e.Name)
                .IsUnique();

            // Many-to-many relationship with Workers
            builder.HasMany(e => e.Workers)
                .WithMany(w => w.Trainings)
                .UsingEntity(
                    "WorkerTraining",
                    l => l.HasOne(typeof(Worker)).WithMany().HasForeignKey("WorkerId").HasPrincipalKey(nameof(Worker.Id)),
                    r => r.HasOne(typeof(Training)).WithMany().HasForeignKey("TrainingId").HasPrincipalKey(nameof(Training.Id)),
                    j => j.HasKey("WorkerId", "TrainingId"));
        }
    }
}
