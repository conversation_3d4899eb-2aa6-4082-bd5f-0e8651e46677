using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class WorkerConfiguration : IEntityTypeConfiguration<Worker>
    {
        public void Configure(EntityTypeBuilder<Worker> builder)
        {
            builder.ToTable("Workers");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Company)
                .IsRequired()
                .HasMaxLength(100);



            builder.Property(e => e.NationalId)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(e => e.PhotoUrl)
                .IsRequired(false)
                .HasMaxLength(500);

            builder.Property(e => e.Gender)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(e => e.PhoneNumber)
                .HasMaxLength(20);

            builder.Property(e => e.Email)
                .HasMaxLength(100);

            // Indexes for better query performance
            builder.HasIndex(e => e.NationalId)
                .IsUnique();

            builder.HasIndex(e => e.Email);
            builder.HasIndex(e => e.Company);
        }
    }
}