﻿using GraphQLApi.Data;
using GraphQLApi.Services;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITraingService _traingService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public Mutation(IWorkerService workerService, ITraingService traingService, IDbContextFactory<AppDbContext> contextFactory)
        {
            _workerService = workerService;
            _traingService = traingService;
            _contextFactory = contextFactory;

        }

        public async Task<Worker> CreateWorker(
            string name,
            string company,
            string trade,
            string skill,
            string nationalId,
            string gender,
            // int age,
            DateOnly dateOfBirth,
            // int? trainingsCompleted = 0,
            List<int>? trainingIds = null,
            int manHours = 0,
            double rating = 0,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            trainingIds ??= [];
            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    Trade = trade,
                    Skill = skill,
                    NationalId = nationalId,
                    Gender = gender,
                    // Age = age,
                    DateOfBirth = dateOfBirth,
                    // TrainingsCompleted = trainingsCompleted,
                    ManHours = manHours,
                    Rating = rating,
                    PhoneNumber = phoneNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };
                await using var context = await _contextFactory.CreateDbContextAsync();
                List<Training> trainings = [];
                foreach (var id in trainingIds)
                {
                    var training = await _traingService.GetTrainingByIdAsync(id) ?? throw new GraphQLException(new Error("Validation", $"Training with id'{id}' does not exists."));
                    context.Trainings.Attach(training);
                    trainings.Add(training);
                }

                var savedWorker = await _workerService.CreateWorkerAsync(worker);

                foreach (var training in trainings)
                {
                    if (!savedWorker.Trainings.Any(t => t.Id == training.Id))
                    {
                        // context.Trainings.Attach(training);
                        savedWorker.Trainings.Add(training);
                    }
                }

                context.Workers.Add(worker);
                await context.SaveChangesAsync();
                return savedWorker;
            }
            catch (GraphQLException)
            {
                throw; // Re-throw GraphQL exceptions
            }
            catch (Exception ex)
            {
                throw new GraphQLException(
                    new Error("Internal Server Error", ex.Message)
                );
            }
        }

        public async Task<Worker?> UpdateWorker(
            int id,
            string? name = null,
            string? company = null,
            string? trade = null,
            string? skill = null,
            int? trainingsCompleted = null,
            List<int>? trainingIds = null,
            int? manHours = null,
            double? rating = null,
            string? gender = null,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            trainingIds ??= [];
            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = name ?? existingWorker.Name,
                Company = company ?? existingWorker.Company,
                Trade = trade ?? existingWorker.Trade,
                Skill = skill ?? existingWorker.Skill,
                TrainingsCompleted = trainingsCompleted ?? existingWorker.TrainingsCompleted,
                ManHours = manHours ?? existingWorker.ManHours,
                Rating = rating ?? existingWorker.Rating,
                Gender = gender ?? existingWorker.Gender,
                PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                Email = email ?? existingWorker.Email,
                InductionDate = inductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            trainingIds.ForEach(async id =>
            {
                var training = await _traingService.GetTrainingByIdAsync(id);
                if (training != null)
                {
                    updatedWorker.Trainings.Add(training);
                }
            });

            return await _workerService.UpdateWorkerAsync(id, updatedWorker);
        }

        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }
        public async Task<Training> CreateTraining(
            string name,
            List<int>? workerIds = null
        )
        {
            workerIds ??= [];
            try
            {
                var training = new Training { Name = name };
                workerIds.ForEach(async id =>
                {
                    var worker = await _workerService.GetWorkerByIdAsync(id);
                    if (worker != null)
                    {
                        training.Workers.Add(worker);
                    }
                });
                return await _traingService.CreateTrainingAsync(training);

            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }
        public async Task<Training?> UpdateTraining(
            int id,
            string? name = null,
            List<int>? workerIds = null
        )
        {
            workerIds ??= [];
            var existingTraining = await _traingService.GetTrainingByIdAsync(id);

            if (existingTraining == null)
            {
                return null;
            }

            var training = new Training
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
            };

            workerIds.ForEach(async id =>
            {
                var worker = await _workerService.GetWorkerByIdAsync(id);
                if (worker != null)
                {
                    training.Workers.Add(worker);
                }
            });

            return await _traingService.UpateTrainingAsync(training.Id, training);
        }
        public async Task<bool> DeleteTraining(int id)
        {
            return await _traingService.DeleteTrainingAsync(id);
        }
    }
}
