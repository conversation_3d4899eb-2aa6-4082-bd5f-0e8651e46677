using GraphQLApi.Data;
using GraphQLApi.Services;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public Mutation(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            IDbContextFactory<AppDbContext> contextFactory)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _contextFactory = contextFactory;
        }

        // Worker Mutations
        public async Task<Worker> CreateWorker(
            string name,
            string company,
            string nationalId,
            string gender,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            List<int>? tradeIds = null,
            List<int>? skillIds = null,
            int manHours = 0,
            double rating = 0,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            trainingIds ??= new List<int>();
            tradeIds ??= new List<int>();
            skillIds ??= new List<int>();

            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    NationalId = nationalId,
                    Gender = gender,
                    DateOfBirth = dateOfBirth,
                    ManHours = manHours,
                    Rating = rating,
                    PhoneNumber = phoneNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };

                // Create worker first
                var createdWorker = await _workerService.CreateWorkerAsync(worker);

                // Add relationships if specified
                if (trainingIds.Any() || tradeIds.Any() || skillIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var workerEntity = await context.Workers
                        .Include(w => w.Trainings)
                        .Include(w => w.Trades)
                        .Include(w => w.Skills)
                        .FirstOrDefaultAsync(w => w.Id == createdWorker.Id);

                    if (workerEntity != null)
                    {
                        // Add trainings
                        foreach (var trainingId in trainingIds)
                        {
                            var training = await context.Trainings.FindAsync(trainingId);
                            if (training != null)
                            {
                                workerEntity.Trainings.Add(training);
                            }
                        }

                        // Add trades
                        foreach (var tradeId in tradeIds)
                        {
                            var trade = await context.Trades.FindAsync(tradeId);
                            if (trade != null)
                            {
                                workerEntity.Trades.Add(trade);
                            }
                        }

                        // Add skills
                        foreach (var skillId in skillIds)
                        {
                            var skill = await context.Skills.FindAsync(skillId);
                            if (skill != null)
                            {
                                workerEntity.Skills.Add(skill);
                            }
                        }

                        await context.SaveChangesAsync();
                    }
                }

                return await _workerService.GetWorkerByIdAsync(createdWorker.Id) ?? createdWorker;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Worker?> UpdateWorker(
            int id,
            string? name = null,
            string? company = null,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            List<int>? tradeIds = null,
            List<int>? skillIds = null,
            int? manHours = null,
            double? rating = null,
            string? gender = null,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = name ?? existingWorker.Name,
                Company = company ?? existingWorker.Company,
                DateOfBirth = dateOfBirth ?? existingWorker.DateOfBirth,
                ManHours = manHours ?? existingWorker.ManHours,
                Rating = rating ?? existingWorker.Rating,
                Gender = gender ?? existingWorker.Gender,
                NationalId = existingWorker.NationalId,
                PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                Email = email ?? existingWorker.Email,
                InductionDate = inductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            var result = await _workerService.UpdateWorkerAsync(id, updatedWorker);

            // Update relationships if specified
            if (trainingIds != null || tradeIds != null || skillIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var workerEntity = await context.Workers
                    .Include(w => w.Trainings)
                    .Include(w => w.Trades)
                    .Include(w => w.Skills)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (workerEntity != null)
                {
                    // Update trainings if specified
                    if (trainingIds != null)
                    {
                        workerEntity.Trainings.Clear();
                        foreach (var trainingId in trainingIds)
                        {
                            var training = await context.Trainings.FindAsync(trainingId);
                            if (training != null)
                            {
                                workerEntity.Trainings.Add(training);
                            }
                        }
                    }

                    // Update trades if specified
                    if (tradeIds != null)
                    {
                        workerEntity.Trades.Clear();
                        foreach (var tradeId in tradeIds)
                        {
                            var trade = await context.Trades.FindAsync(tradeId);
                            if (trade != null)
                            {
                                workerEntity.Trades.Add(trade);
                            }
                        }
                    }

                    // Update skills if specified
                    if (skillIds != null)
                    {
                        workerEntity.Skills.Clear();
                        foreach (var skillId in skillIds)
                        {
                            var skill = await context.Skills.FindAsync(skillId);
                            if (skill != null)
                            {
                                workerEntity.Skills.Add(skill);
                            }
                        }
                    }

                    await context.SaveChangesAsync();
                }
            }

            return await _workerService.GetWorkerByIdAsync(id);
        }

        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }

        // Training Mutations
        public async Task<Training> CreateTraining(
            string name,
            string? description = null,
            DateTime? startDate = null,
            TimeSpan? duration = null,
            string? trainingType = null,
            string? trainer = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var training = new Training
                {
                    Name = name,
                    Description = description,
                    StartDate = startDate,
                    Duration = duration,
                    TrainingType = trainingType,
                    Trainer = trainer
                };

                var createdTraining = await _trainingService.CreateTrainingAsync(training);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var trainingEntity = await context.Trainings
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTraining.Id);

                    if (trainingEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                trainingEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _trainingService.GetTrainingByIdAsync(createdTraining.Id) ?? createdTraining;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Training?> UpdateTraining(
            int id,
            string? name = null,
            string? description = null,
            DateTime? startDate = null,
            TimeSpan? duration = null,
            string? trainingType = null,
            string? trainer = null,
            List<int>? workerIds = null)
        {
            var existingTraining = await _trainingService.GetTrainingByIdAsync(id);
            if (existingTraining == null)
                return null;

            var updatedTraining = new Training
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
                Description = description ?? existingTraining.Description,
                StartDate = startDate ?? existingTraining.StartDate,
                Duration = duration ?? existingTraining.Duration,
                TrainingType = trainingType ?? existingTraining.TrainingType,
                Trainer = trainer ?? existingTraining.Trainer
            };

            var result = await _trainingService.UpdateTrainingAsync(id, updatedTraining);

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var trainingEntity = await context.Trainings
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (trainingEntity != null)
                {
                    // Clear existing workers
                    trainingEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            trainingEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<bool> DeleteTraining(int id)
        {
            return await _trainingService.DeleteTrainingAsync(id);
        }

        // Trade Mutations
        public async Task<Trade> CreateTrade(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var trade = new Trade
                {
                    Name = name,
                    Description = description
                };

                var createdTrade = await _tradeService.CreateTradeAsync(trade);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var tradeEntity = await context.Trades
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTrade.Id);

                    if (tradeEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                tradeEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _tradeService.GetTradeByIdAsync(createdTrade.Id) ?? createdTrade;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Trade?> UpdateTrade(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingTrade = await _tradeService.GetTradeByIdAsync(id);
            if (existingTrade == null)
                return null;

            var updatedTrade = new Trade
            {
                Id = existingTrade.Id,
                Name = name ?? existingTrade.Name,
                Description = description ?? existingTrade.Description
            };

            var result = await _tradeService.UpdateTradeAsync(id, updatedTrade);

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var tradeEntity = await context.Trades
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (tradeEntity != null)
                {
                    // Clear existing workers
                    tradeEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            tradeEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<bool> DeleteTrade(int id)
        {
            return await _tradeService.DeleteTradeAsync(id);
        }

        // Skill Mutations
        public async Task<Skill> CreateSkill(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var skill = new Skill
                {
                    Name = name,
                    Description = description
                };

                var createdSkill = await _skillService.CreateSkillAsync(skill);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var skillEntity = await context.Skills
                        .Include(s => s.Workers)
                        .FirstOrDefaultAsync(s => s.Id == createdSkill.Id);

                    if (skillEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                skillEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _skillService.GetSkillByIdAsync(createdSkill.Id) ?? createdSkill;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Skill?> UpdateSkill(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingSkill = await _skillService.GetSkillByIdAsync(id);
            if (existingSkill == null)
                return null;

            var updatedSkill = new Skill
            {
                Id = existingSkill.Id,
                Name = name ?? existingSkill.Name,
                Description = description ?? existingSkill.Description
            };

            var result = await _skillService.UpdateSkillAsync(id, updatedSkill);

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var skillEntity = await context.Skills
                    .Include(s => s.Workers)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (skillEntity != null)
                {
                    // Clear existing workers
                    skillEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            skillEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _skillService.GetSkillByIdAsync(id);
        }

        public async Task<bool> DeleteSkill(int id)
        {
            return await _skillService.DeleteSkillAsync(id);
        }
    }
}
