﻿using GraphQLApi.Data;
using GraphQLApi.Services;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public Mutation(
            IWorkerService workerService,
            ITrainingService trainingService,
            IDbContextFactory<AppDbContext> contextFactory)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _contextFactory = contextFactory;
        }

        // Worker Mutations
        public async Task<Worker> CreateWorker(
            string name,
            string company,
            string trade,
            string skill,
            string nationalId,
            string gender,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            int manHours = 0,
            double rating = 0,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            trainingIds ??= new List<int>();

            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    Trade = trade,
                    Skill = skill,
                    NationalId = nationalId,
                    Gender = gender,
                    DateOfBirth = dateOfBirth,
                    ManHours = manHours,
                    Rating = rating,
                    PhoneNumber = phoneNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };

                // Create worker first
                var createdWorker = await _workerService.CreateWorkerAsync(worker);

                // Add trainings if specified
                if (trainingIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var workerEntity = await context.Workers
                        .Include(w => w.Trainings)
                        .FirstOrDefaultAsync(w => w.Id == createdWorker.Id);

                    if (workerEntity != null)
                    {
                        foreach (var trainingId in trainingIds)
                        {
                            var training = await context.Trainings.FindAsync(trainingId);
                            if (training != null)
                            {
                                workerEntity.Trainings.Add(training);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _workerService.GetWorkerByIdAsync(createdWorker.Id) ?? createdWorker;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Worker?> UpdateWorker(
            int id,
            string? name = null,
            string? company = null,
            string? trade = null,
            string? skill = null,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            int? manHours = null,
            double? rating = null,
            string? gender = null,
            string? phoneNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = name ?? existingWorker.Name,
                Company = company ?? existingWorker.Company,
                Trade = trade ?? existingWorker.Trade,
                Skill = skill ?? existingWorker.Skill,
                DateOfBirth = dateOfBirth ?? existingWorker.DateOfBirth,
                ManHours = manHours ?? existingWorker.ManHours,
                Rating = rating ?? existingWorker.Rating,
                Gender = gender ?? existingWorker.Gender,
                NationalId = existingWorker.NationalId,
                PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                Email = email ?? existingWorker.Email,
                InductionDate = inductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            var result = await _workerService.UpdateWorkerAsync(id, updatedWorker);

            // Update trainings if specified
            if (trainingIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var workerEntity = await context.Workers
                    .Include(w => w.Trainings)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (workerEntity != null)
                {
                    // Clear existing trainings
                    workerEntity.Trainings.Clear();

                    // Add new trainings
                    foreach (var trainingId in trainingIds)
                    {
                        var training = await context.Trainings.FindAsync(trainingId);
                        if (training != null)
                        {
                            workerEntity.Trainings.Add(training);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _workerService.GetWorkerByIdAsync(id);
        }

        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }

        // Training Mutations
        public async Task<Training> CreateTraining(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var training = new Training
                {
                    Name = name,
                    Description = description
                };

                var createdTraining = await _trainingService.CreateTrainingAsync(training);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var trainingEntity = await context.Trainings
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTraining.Id);

                    if (trainingEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                trainingEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _trainingService.GetTrainingByIdAsync(createdTraining.Id) ?? createdTraining;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Training?> UpdateTraining(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingTraining = await _trainingService.GetTrainingByIdAsync(id);
            if (existingTraining == null)
                return null;

            var updatedTraining = new Training
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
                Description = description ?? existingTraining.Description
            };

            var result = await _trainingService.UpdateTrainingAsync(id, updatedTraining);

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var trainingEntity = await context.Trainings
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (trainingEntity != null)
                {
                    // Clear existing workers
                    trainingEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            trainingEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<bool> DeleteTraining(int id)
        {
            return await _trainingService.DeleteTrainingAsync(id);
        }
    }
}
