﻿using HotChocolate.Types;
using Shared.GraphQL.Models;
using HotChocolate;
using Task = Shared.GraphQL.Models.Task;

namespace Shared.GraphQL.Types
{
    public class TaskType : ObjectType<Task>
    {
        protected override void Configure(IObjectTypeDescriptor<Task> descriptor)
        {
            descriptor.Field(x => x.Id).Type<NonNullType<IntType>>();
            descriptor.Field(x => x.Name).Type<NonNullType<StringType>>();
            descriptor.Field(x => x.Type).Type<NonNullType<StringType>>();
            descriptor.Field(x => x.Description).Type<NonNullType<StringType>>();
            descriptor.Field(x => x.AssignedWorkerId).Type<NonNullType<StringType>>();

        }

    }
}
